import SwiftUI

struct DotsView: View {
    @EnvironmentObject private var authManager: AuthenticationManager
    @StateObject private var apiService = APIService.shared
    @State private var dots: [Dot] = []
    @State private var isLoading = false
    @State private var errorMessage = ""
    @State private var selectedDot: Dot?
    @State private var showingDotDetail = false
    
    var body: some View {
        NavigationView {
            VStack {
                if isLoading {
                    ProgressView("Loading dots...")
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else if dots.isEmpty && !errorMessage.isEmpty {
                    VStack(spacing: 16) {
                        Image(systemName: "exclamationmark.triangle")
                            .font(.largeTitle)
                            .foregroundColor(.orange)
                        
                        Text("Error Loading Dots")
                            .font(.headline)
                        
                        Text(errorMessage)
                            .font(.body)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                        
                        Button("Retry") {
                            Task {
                                await loadDots()
                            }
                        }
                        .buttonStyle(.borderedProminent)
                    }
                    .padding()
                } else if dots.isEmpty {
                    VStack(spacing: 16) {
                        Image(systemName: "circle.grid.2x2")
                            .font(.largeTitle)
                            .foregroundColor(.gray)
                        
                        Text("No Dots Yet")
                            .font(.headline)
                        
                        Text("Start creating your first thinking dot!")
                            .font(.body)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                } else {
                    List(dots) { dot in
                        DotRowView(dot: dot)
                            .onTapGesture {
                                selectedDot = dot
                                showingDotDetail = true
                            }
                    }
                    .refreshable {
                        await loadDots()
                    }
                }
            }
            .navigationTitle("My Dots")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Fetch Dot 128") {
                        Task {
                            await fetchSpecificDot(id: "128")
                        }
                    }
                }
                
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Sign Out") {
                        authManager.signOut()
                    }
                }
            }
            .sheet(isPresented: $showingDotDetail) {
                if let dot = selectedDot {
                    NavigationView {
                        DotDetailView(dot: dot)
                            .navigationBarTitleDisplayMode(.inline)
                            .toolbar {
                                ToolbarItem(placement: .navigationBarTrailing) {
                                    Button("Done") {
                                        showingDotDetail = false
                                    }
                                }
                            }
                    }
                }
            }
        }
        .task {
            await loadDots()
        }
    }
    
    private func loadDots() async {
        isLoading = true
        errorMessage = ""
        
        do {
            let token = try await authManager.getIdToken()
            let response = try await apiService.listDots(authToken: token)
            
            await MainActor.run {
                if response.success {
                    self.dots = response.data ?? []
                } else {
                    self.errorMessage = response.error ?? "Unknown error"
                }
                self.isLoading = false
            }
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
                self.isLoading = false
            }
        }
    }
    
    private func fetchSpecificDot(id: String) async {
        isLoading = true
        errorMessage = ""
        
        do {
            let token = try await authManager.getIdToken()
            let dot = try await apiService.getDot(id: id, authToken: token)
            
            await MainActor.run {
                self.selectedDot = dot
                self.showingDotDetail = true
                self.isLoading = false
            }
        } catch {
            await MainActor.run {
                self.errorMessage = error.localizedDescription
                self.isLoading = false
            }
        }
    }
}

struct DotRowView: View {
    let dot: Dot
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Label(DotType(rawValue: dot.type)?.displayName ?? "Unknown", 
                      systemImage: DotType(rawValue: dot.type)?.icon ?? "doc")
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Spacer()
                
                Text(BizType(rawValue: dot.bizType)?.displayName ?? "Unknown")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.gray.opacity(0.2))
                    .cornerRadius(6)
            }
            
            if let title = dot.content.title, !title.isEmpty {
                Text(title)
                    .font(.headline)
                    .lineLimit(2)
            }
            
            if let text = dot.content.text, !text.isEmpty {
                Text(text)
                    .font(.body)
                    .lineLimit(3)
                    .foregroundColor(.secondary)
            }
            
            if !dot.tags.isEmpty {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 4) {
                        ForEach(dot.tags, id: \.name) { tag in
                            Text("#\(tag.name)")
                                .font(.caption)
                                .foregroundColor(.blue)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color.blue.opacity(0.1))
                                .cornerRadius(8)
                        }
                    }
                    .padding(.horizontal, 1)
                }
            }
        }
        .padding(.vertical, 4)
    }
}

#Preview {
    @Previewable @StateObject var authManager = AuthenticationManager()
    
    DotsView()
        .environmentObject(authManager)
} 