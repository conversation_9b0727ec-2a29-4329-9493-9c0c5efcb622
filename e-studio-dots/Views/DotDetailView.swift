import SwiftUI

struct DotDetailView: View {
    let dot: Dot
    
    var body: some View {
        ScrollView {
            VStack(alignment: .leading, spacing: 16) {
                // Header with type and business type
                HStack {
                    Label(DotType(rawValue: dot.type)?.displayName ?? "Unknown", 
                          systemImage: DotType(rawValue: dot.type)?.icon ?? "doc")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text(BizType(rawValue: dot.bizType)?.displayName ?? "Unknown")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.gray.opacity(0.2))
                        .cornerRadius(8)
                }
                
                // Title if available
                if let title = dot.content.title, !title.isEmpty {
                    Text(title)
                        .font(.title2)
                        .fontWeight(.semibold)
                }
                
                // Main content
                if let text = dot.content.text, !text.isEmpty {
                    Text(text)
                        .font(.body)
                        .lineLimit(nil)
                }
                
                // URL if available
                if let urlString = dot.content.url, !urlString.isEmpty {
                    Link(destination: URL(string: urlString) ?? URL(string: "https://example.com")!) {
                        HStack {
                            Image(systemName: "link")
                            Text(urlString)
                                .lineLimit(1)
                        }
                        .foregroundColor(.blue)
                    }
                }
                
                // Tags
                if !dot.tags.isEmpty {
                    LazyVGrid(columns: [GridItem(.adaptive(minimum: 80))], spacing: 8) {
                        ForEach(dot.tags, id: \.name) { tag in
                            Text("#\(tag.name)")
                                .font(.caption)
                                .foregroundColor(.blue)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.blue.opacity(0.1))
                                .cornerRadius(12)
                        }
                    }
                }
                
                Spacer()
                
                // Footer with dates
                VStack(alignment: .leading, spacing: 4) {
                    Text("Created: \(formatDate(dot.createdAt))")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    if dot.createdAt != dot.updatedAt {
                        Text("Updated: \(formatDate(dot.updatedAt))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding()
        }
        .navigationTitle("Dot Details")
        .navigationBarTitleDisplayMode(.inline)
    }
    
    private func formatDate(_ dateString: String) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"
        
        if let date = formatter.date(from: dateString) {
            let displayFormatter = DateFormatter()
            displayFormatter.dateStyle = .medium
            displayFormatter.timeStyle = .short
            return displayFormatter.string(from: date)
        }
        
        return dateString
    }
}

#Preview {
    @Previewable let sampleDot = Dot(
        id: "1",
        uuid: "uuid-1",
        userId: "user-1",
        type: 0,
        bizType: 0,
        content: DotContent(
            text: "This is a sample dot content that demonstrates how the UI looks with longer text content.",
            title: "Sample Dot",
            url: "https://example.com",
            meta: nil
        ),
        tags: [
            DotTag(name: "inspiration", slug: "inspiration"),
            DotTag(name: "ideas", slug: "ideas")
        ],
        createdAt: "2024-01-01T12:00:00.000Z",
        updatedAt: "2024-01-01T12:00:00.000Z"
    )
    
    NavigationView {
        DotDetailView(dot: sampleDot)
    }
} 