import Foundation
import Combine

class APIService: ObservableObject {
    static let shared = APIService()
    
    private let baseURL = "http://localhost:3002/api"
    private let session = URLSession.shared
    
    private init() {}
    
    // MARK: - Dots API
    func getDot(id: String, authToken: String) async throws -> Dot {
        let url = URL(string: "\(baseURL)/dots/\(id)")!
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(authToken)", forHT<PERSON>HeaderField: "Authorization")
        request.setValue("true", forHT<PERSON>HeaderField: "x-firebase-token-auth")
        
        let (data, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.invalidResponse
        }
        
        if httpResponse.statusCode == 401 {
            throw APIError.unauthorized
        }
        
        guard httpResponse.statusCode == 200 else {
            throw APIError.serverError(httpResponse.statusCode)
        }
        
        let decoder = JSONDecoder()
        let dotResponse = try decoder.decode(DotResponse.self, from: data)
        
        if !dotResponse.success {
            throw APIError.apiError(dotResponse.error ?? "Unknown error")
        }
        
        guard let dot = dotResponse.data else {
            throw APIError.noData
        }
        
        return dot
    }
    
    func listDots(authToken: String, page: Int = 1, pageSize: Int = 20) async throws -> DotsListResponse {
        let url = URL(string: "\(baseURL)/dots?page=\(page)&pageSize=\(pageSize)")!
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(authToken)", forHTTPHeaderField: "Authorization")
        request.setValue("true", forHTTPHeaderField: "x-firebase-token-auth")
        
        let (data, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.invalidResponse
        }
        
        if httpResponse.statusCode == 401 {
            throw APIError.unauthorized
        }
        
        guard httpResponse.statusCode == 200 else {
            throw APIError.serverError(httpResponse.statusCode)
        }
        
        let decoder = JSONDecoder()
        let dotsResponse = try decoder.decode(DotsListResponse.self, from: data)
        return dotsResponse
    }
    
    func getRandomDot(authToken: String) async throws -> Dot? {
        let url = URL(string: "\(baseURL)/dots?shuffle=true")!
        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(authToken)", forHTTPHeaderField: "Authorization")
        request.setValue("true", forHTTPHeaderField: "x-firebase-token-auth")
        
        let (data, response) = try await session.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.invalidResponse
        }
        
        if httpResponse.statusCode == 401 {
            throw APIError.unauthorized
        }
        
        guard httpResponse.statusCode == 200 else {
            throw APIError.serverError(httpResponse.statusCode)
        }
        
        let decoder = JSONDecoder()
        let dotResponse = try decoder.decode(DotResponse.self, from: data)
        
        if !dotResponse.success {
            throw APIError.apiError(dotResponse.error ?? "Unknown error")
        }
        
        return dotResponse.data
    }
}

// MARK: - API Errors
enum APIError: LocalizedError {
    case invalidURL
    case invalidResponse
    case noData
    case unauthorized
    case authenticationRequired
    case serverError(Int)
    case apiError(String)
    case decodingError
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid URL"
        case .invalidResponse:
            return "Invalid response"
        case .noData:
            return "No data received"
        case .unauthorized:
            return "Unauthorized - please login again"
        case .authenticationRequired:
            return "Authentication required"
        case .serverError(let code):
            return "Server error: \(code)"
        case .apiError(let message):
            return "API error: \(message)"
        case .decodingError:
            return "Failed to decode response"
        }
    }
} 