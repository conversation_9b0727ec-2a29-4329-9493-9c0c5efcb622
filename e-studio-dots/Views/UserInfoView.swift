//
//  UserInfoView.swift
//  e-studio-dots
//
//  Created by A<PERSON> on 2025/7/15.
//

import SwiftUI
import FirebaseAuth

struct UserInfoView: View {
    @ObservedObject var authManager: AuthenticationManager
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // User Profile Header
                    VStack(spacing: 16) {
                        // Profile Image
                        AsyncImage(url: authManager.user?.photoURL) { image in
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                        } placeholder: {
                            Image(systemName: "person.circle.fill")
                                .font(.system(size: 80))
                                .foregroundStyle(.gray)
                        }
                        .frame(width: 80, height: 80)
                        .clipShape(Circle())
                        
                        // User Name
                        Text(authManager.user?.displayName ?? "Unknown User")
                            .font(.title2)
                            .fontWeight(.semibold)
                        
                        // User Email
                        Text(authManager.user?.email ?? "No email")
                            .font(.subheadline)
                            .foregroundStyle(.secondary)
                    }
                    .padding(.top, 20)
                    
                    // User Information Cards
                    LazyVStack(spacing: 16) {
                        let userInfo = authManager.getUserInfo()
                        
                        InfoCard(title: "User ID", value: userInfo["uid"] as? String ?? "N/A", icon: "person.badge.key")
                        
                        InfoCard(title: "Email", value: userInfo["email"] as? String ?? "N/A", icon: "envelope")
                        
                        InfoCard(title: "Display Name", value: userInfo["displayName"] as? String ?? "N/A", icon: "person.text.rectangle")
                        
                        InfoCard(title: "Email Verified", value: (userInfo["isEmailVerified"] as? Bool ?? false) ? "Yes" : "No", icon: "checkmark.shield")
                        
                        InfoCard(title: "Account Created", value: formatDate(userInfo["creationDate"] as? String), icon: "calendar.badge.plus")
                        
                        InfoCard(title: "Last Sign In", value: formatDate(userInfo["lastSignInDate"] as? String), icon: "clock.arrow.circlepath")
                    }
                    .padding(.horizontal)
                    
                    // Sign Out Button
                    Button(action: {
                        authManager.signOut()
                    }) {
                        HStack {
                            Image(systemName: "rectangle.portrait.and.arrow.right")
                                .font(.headline)
                            
                            Text("Sign Out")
                                .font(.headline)
                                .fontWeight(.medium)
                        }
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .frame(height: 50)
                        .background(Color.red.gradient)
                        .cornerRadius(12)
                    }
                    .padding(.horizontal)
                    .padding(.top, 20)
                }
                .padding(.bottom, 30)
            }
            .navigationTitle("User Profile")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
    
    private func formatDate(_ dateString: String?) -> String {
        guard let dateString = dateString else { return "N/A" }
        
        // Simple date formatting - you can enhance this
        if dateString.contains("Optional(") {
            return dateString.replacingOccurrences(of: "Optional(", with: "").replacingOccurrences(of: ")", with: "")
        }
        return dateString
    }
}

struct InfoCard: View {
    let title: String
    let value: String
    let icon: String
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .font(.title2)
                .foregroundStyle(.blue)
                .frame(width: 30)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.caption)
                    .foregroundStyle(.secondary)
                    .textCase(.uppercase)
                
                Text(value)
                    .font(.body)
                    .fontWeight(.medium)
            }
            
            Spacer()
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

#Preview {
    @Previewable @StateObject var authManager = AuthenticationManager()
    
    UserInfoView(authManager: authManager)
} 
