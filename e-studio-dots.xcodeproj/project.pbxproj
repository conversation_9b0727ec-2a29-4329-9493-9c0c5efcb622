// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 90;
	objects = {

/* Begin PBXBuildFile section */
		11F61CB92E26245B0049DF94 /* FirebaseAnalytics in Frameworks */ = {isa = PBXBuildFile; productRef = 11F61CB82E26245B0049DF94 /* FirebaseAnalytics */; };
		11F61CBB2E26245B0049DF94 /* FirebaseAnalyticsCore in Frameworks */ = {isa = PBXBuildFile; productRef = 11F61CBA2E26245B0049DF94 /* FirebaseAnalyticsCore */; };
		11F61CBD2E26245B0049DF94 /* FirebaseAnalyticsIdentitySupport in Frameworks */ = {isa = PBXBuildFile; productRef = 11F61CBC2E26245B0049DF94 /* FirebaseAnalyticsIdentitySupport */; };
		11F61CBF2E26245B0049DF94 /* FirebaseAnalyticsOnDeviceConversion in Frameworks */ = {isa = PBXBuildFile; productRef = 11F61CBE2E26245B0049DF94 /* FirebaseAnalyticsOnDeviceConversion */; };
		11F61CF32E2624F60049DF94 /* FirebaseCore in Frameworks */ = {isa = PBXBuildFile; productRef = 11F61CF22E2624F60049DF94 /* FirebaseCore */; };
		11F61D042E2629260049DF94 /* GoogleSignIn in Frameworks */ = {isa = PBXBuildFile; productRef = 11F61D032E2629260049DF94 /* GoogleSignIn */; };
		11F61D062E2629260049DF94 /* GoogleSignInSwift in Frameworks */ = {isa = PBXBuildFile; productRef = 11F61D052E2629260049DF94 /* GoogleSignInSwift */; };
		11F61D082E2629B80049DF94 /* FirebaseAuth in Frameworks */ = {isa = PBXBuildFile; productRef = 11F61D072E2629B80049DF94 /* FirebaseAuth */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		11BBFF082E260F30006120CA /* e-studio-dots.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "e-studio-dots.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		11D7A4C42E2617E900F2EC9A /* Promises.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = Promises.xcodeproj; sourceTree = "<group>"; };
		11D7AC6F2E2617EB00F2EC9A /* FirebasePodTest.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = FirebasePodTest.xcodeproj; sourceTree = "<group>"; };
		11D7AD1B2E2617EB00F2EC9A /* CombineSample.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = CombineSample.xcodeproj; sourceTree = "<group>"; };
		11D7AD412E2617EB00F2EC9A /* FirestoreSample.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = FirestoreSample.xcodeproj; sourceTree = "<group>"; };
		11D7AD512E2617EB00F2EC9A /* tvOSSample.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = tvOSSample.xcodeproj; sourceTree = "<group>"; };
		11D7AD652E2617EB00F2EC9A /* SampleWatchApp.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = SampleWatchApp.xcodeproj; sourceTree = "<group>"; };
		11D7AD842E2617EB00F2EC9A /* ABTQA.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = ABTQA.xcodeproj; sourceTree = "<group>"; };
		11D7B0792E2617EB00F2EC9A /* FirebaseAITestApp.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = FirebaseAITestApp.xcodeproj; sourceTree = "<group>"; };
		11D7B0E32E2617EC00F2EC9A /* AppCheckCustomProvideApp.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = AppCheckCustomProvideApp.xcodeproj; sourceTree = "<group>"; };
		11D7B0EF2E2617EC00F2EC9A /* FIRAppCheckTestApp.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = FIRAppCheckTestApp.xcodeproj; sourceTree = "<group>"; };
		11D7B27E2E2617EC00F2EC9A /* AuthenticationExample.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = AuthenticationExample.xcodeproj; sourceTree = "<group>"; };
		11D7B51E2E2617EC00F2EC9A /* FDLBuilderTestAppObjC.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = FDLBuilderTestAppObjC.xcodeproj; sourceTree = "<group>"; };
		11D7B6092E2617EC00F2EC9A /* FIAMSwiftUI.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = FIAMSwiftUI.xcodeproj; sourceTree = "<group>"; };
		11D7B6272E2617EC00F2EC9A /* InAppMessagingDisplay-Sample.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = "InAppMessagingDisplay-Sample.xcodeproj"; sourceTree = "<group>"; };
		11D7B6412E2617EC00F2EC9A /* InAppMessaging-Example-iOS.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = "InAppMessaging-Example-iOS.xcodeproj"; sourceTree = "<group>"; };
		11D7B6E32E2617EC00F2EC9A /* AdvancedSample.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = AdvancedSample.xcodeproj; sourceTree = "<group>"; };
		11D7B6EE2E2617EC00F2EC9A /* Sample.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = Sample.xcodeproj; sourceTree = "<group>"; };
		11D7B6F92E2617EC00F2EC9A /* SampleStandaloneWatchApp.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = SampleStandaloneWatchApp.xcodeproj; sourceTree = "<group>"; };
		11D7B70F2E2617EC00F2EC9A /* SwiftUISample.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = SwiftUISample.xcodeproj; sourceTree = "<group>"; };
		11D7B79F2E2617EC00F2EC9A /* MLDownloaderTestApp.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = MLDownloaderTestApp.xcodeproj; sourceTree = "<group>"; };
		11D7B86D2E2617EC00F2EC9A /* FIRPerfE2E.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = FIRPerfE2E.xcodeproj; sourceTree = "<group>"; };
		11D7B8B62E2617EC00F2EC9A /* PerfTestRigApp.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = PerfTestRigApp.xcodeproj; sourceTree = "<group>"; };
		11D7B94C2E2617EC00F2EC9A /* FeatureRolloutsTestApp.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = FeatureRolloutsTestApp.xcodeproj; sourceTree = "<group>"; };
		11D7B9622E2617EC00F2EC9A /* RemoteConfigSampleApp.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = RemoteConfigSampleApp.xcodeproj; sourceTree = "<group>"; };
		11D7B9A02E2617EC00F2EC9A /* SwiftUISample.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = SwiftUISample.xcodeproj; sourceTree = "<group>"; };
		11D7B9D72E2617EC00F2EC9A /* AppQualityDevApp.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = AppQualityDevApp.xcodeproj; sourceTree = "<group>"; };
		11D7BE052E2617EC00F2EC9A /* Firestore.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = Firestore.xcodeproj; sourceTree = "<group>"; };
		11D7BE9A2E2617EC00F2EC9A /* FrameworkMaker.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = FrameworkMaker.xcodeproj; sourceTree = "<group>"; };
		11D7CBA42E2617EE00F2EC9A /* ClientApp.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = ClientApp.xcodeproj; sourceTree = "<group>"; };
		11D7CBBA2E2617EE00F2EC9A /* CocoapodsIntegrationTest.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = CocoapodsIntegrationTest.xcodeproj; sourceTree = "<group>"; };
		11D7CCC72E2617EE00F2EC9A /* SymbolCollisionTest.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = SymbolCollisionTest.xcodeproj; sourceTree = "<group>"; };
		11D7D1BF2E2617F200F2EC9A /* GDTCCTWatchOSTestApp.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = GDTCCTWatchOSTestApp.xcodeproj; sourceTree = "<group>"; };
		11D7D25F2E2617F200F2EC9A /* GDTWatchOSTestApp.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = GDTWatchOSTestApp.xcodeproj; sourceTree = "<group>"; };
		11D7D2C92E2617F300F2EC9A /* TestApps.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; path = TestApps.xcodeproj; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		11BBFF0A2E260F30006120CA /* e-studio-dots */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = "e-studio-dots";
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		11BBFF052E260F30006120CA /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			files = (
				11F61CB92E26245B0049DF94 /* FirebaseAnalytics in Frameworks */,
				11F61CF32E2624F60049DF94 /* FirebaseCore in Frameworks */,
				11F61D062E2629260049DF94 /* GoogleSignInSwift in Frameworks */,
				11F61CBB2E26245B0049DF94 /* FirebaseAnalyticsCore in Frameworks */,
				11F61CBD2E26245B0049DF94 /* FirebaseAnalyticsIdentitySupport in Frameworks */,
				11F61CBF2E26245B0049DF94 /* FirebaseAnalyticsOnDeviceConversion in Frameworks */,
				11F61D042E2629260049DF94 /* GoogleSignIn in Frameworks */,
				11F61D082E2629B80049DF94 /* FirebaseAuth in Frameworks */,
			);
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		11BBFEFF2E260F30006120CA = {
			isa = PBXGroup;
			children = (
				11BBFF0A2E260F30006120CA /* e-studio-dots */,
				11F61CF12E2624F60049DF94 /* Frameworks */,
				11BBFF092E260F30006120CA /* Products */,
			);
			sourceTree = "<group>";
		};
		11BBFF092E260F30006120CA /* Products */ = {
			isa = PBXGroup;
			children = (
				11BBFF082E260F30006120CA /* e-studio-dots.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7A4CB2E2617E900F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7AD072E2617EB00F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7AD9D2E2617EB00F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7AD9F2E2617EB00F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7ADA12E2617EB00F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7ADA32E2617EB00F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7ADA52E2617EB00F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7BF3D2E2617EC00F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7BF3F2E2617EC00F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7BF412E2617EC00F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7BF432E2617EC00F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7BF452E2617EC00F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7BF472E2617EC00F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7BF492E2617EC00F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7BF4B2E2617EC00F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7BF4D2E2617EC00F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7BF4F2E2617EC00F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7BF512E2617EC00F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7BF532E2617EC00F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7BF552E2617EC00F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7BF572E2617EC00F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7BF592E2617EC00F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7BF5B2E2617EC00F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7BF5D2E2617EC00F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7BF5F2E2617EC00F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7BF612E2617EC00F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7BF632E2617EC00F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7BF652E2617EC00F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7CCC92E2617EE00F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7CCCB2E2617EE00F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7CCCD2E2617EE00F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7D28B2E2617F200F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7D28D2E2617F200F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11D7D2F12E2617F300F2EC9A /* Products */ = {
			isa = PBXGroup;
			children = (
			);
			name = Products;
			sourceTree = "<group>";
		};
		11F61CF12E2624F60049DF94 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		11BBFF072E260F30006120CA /* e-studio-dots */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 11BBFF152E260F31006120CA /* Build configuration list for PBXNativeTarget "e-studio-dots" */;
			buildPhases = (
				11BBFF042E260F30006120CA /* Sources */,
				11BBFF052E260F30006120CA /* Frameworks */,
				11BBFF062E260F30006120CA /* Resources */,
			);
			buildRules = (
			);
			fileSystemSynchronizedGroups = (
				11BBFF0A2E260F30006120CA /* e-studio-dots */,
			);
			name = "e-studio-dots";
			packageProductDependencies = (
				11F61CB82E26245B0049DF94 /* FirebaseAnalytics */,
				11F61CBA2E26245B0049DF94 /* FirebaseAnalyticsCore */,
				11F61CBC2E26245B0049DF94 /* FirebaseAnalyticsIdentitySupport */,
				11F61CBE2E26245B0049DF94 /* FirebaseAnalyticsOnDeviceConversion */,
				11F61CF22E2624F60049DF94 /* FirebaseCore */,
				11F61D032E2629260049DF94 /* GoogleSignIn */,
				11F61D052E2629260049DF94 /* GoogleSignInSwift */,
				11F61D072E2629B80049DF94 /* FirebaseAuth */,
			);
			productName = "e-studio-dots";
			productReference = 11BBFF082E260F30006120CA /* e-studio-dots.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		11BBFF002E260F30006120CA /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 2600;
				LastUpgradeCheck = 2600;
				TargetAttributes = {
					11BBFF072E260F30006120CA = {
						CreatedOnToolsVersion = 26.0;
					};
				};
			};
			buildConfigurationList = 11BBFF032E260F30006120CA /* Build configuration list for PBXProject "e-studio-dots" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 11BBFEFF2E260F30006120CA;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				11F61CB72E26245B0049DF94 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */,
				11F61D022E2629260049DF94 /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */,
			);
			preferredProjectObjectVersion = 90;
			productRefGroup = 11BBFF092E260F30006120CA /* Products */;
			projectDirPath = "";
			projectReferences = (
				{
					ProductGroup = 11D7ADA52E2617EB00F2EC9A /* Products */;
					ProjectRef = 11D7AD842E2617EB00F2EC9A /* ABTQA.xcodeproj */;
				},
				{
					ProductGroup = 11D7BF5B2E2617EC00F2EC9A /* Products */;
					ProjectRef = 11D7B6E32E2617EC00F2EC9A /* AdvancedSample.xcodeproj */;
				},
				{
					ProductGroup = 11D7BF472E2617EC00F2EC9A /* Products */;
					ProjectRef = 11D7B0E32E2617EC00F2EC9A /* AppCheckCustomProvideApp.xcodeproj */;
				},
				{
					ProductGroup = 11D7BF3D2E2617EC00F2EC9A /* Products */;
					ProjectRef = 11D7B9D72E2617EC00F2EC9A /* AppQualityDevApp.xcodeproj */;
				},
				{
					ProductGroup = 11D7BF512E2617EC00F2EC9A /* Products */;
					ProjectRef = 11D7B27E2E2617EC00F2EC9A /* AuthenticationExample.xcodeproj */;
				},
				{
					ProductGroup = 11D7CCCB2E2617EE00F2EC9A /* Products */;
					ProjectRef = 11D7CBA42E2617EE00F2EC9A /* ClientApp.xcodeproj */;
				},
				{
					ProductGroup = 11D7CCCD2E2617EE00F2EC9A /* Products */;
					ProjectRef = 11D7CBBA2E2617EE00F2EC9A /* CocoapodsIntegrationTest.xcodeproj */;
				},
				{
					ProductGroup = 11D7ADA32E2617EB00F2EC9A /* Products */;
					ProjectRef = 11D7AD1B2E2617EB00F2EC9A /* CombineSample.xcodeproj */;
				},
				{
					ProductGroup = 11D7BF452E2617EC00F2EC9A /* Products */;
					ProjectRef = 11D7B51E2E2617EC00F2EC9A /* FDLBuilderTestAppObjC.xcodeproj */;
				},
				{
					ProductGroup = 11D7BF4F2E2617EC00F2EC9A /* Products */;
					ProjectRef = 11D7B94C2E2617EC00F2EC9A /* FeatureRolloutsTestApp.xcodeproj */;
				},
				{
					ProductGroup = 11D7BF3F2E2617EC00F2EC9A /* Products */;
					ProjectRef = 11D7B6092E2617EC00F2EC9A /* FIAMSwiftUI.xcodeproj */;
				},
				{
					ProductGroup = 11D7BF492E2617EC00F2EC9A /* Products */;
					ProjectRef = 11D7B0EF2E2617EC00F2EC9A /* FIRAppCheckTestApp.xcodeproj */;
				},
				{
					ProductGroup = 11D7BF592E2617EC00F2EC9A /* Products */;
					ProjectRef = 11D7B0792E2617EB00F2EC9A /* FirebaseAITestApp.xcodeproj */;
				},
				{
					ProductGroup = 11D7AD072E2617EB00F2EC9A /* Products */;
					ProjectRef = 11D7AC6F2E2617EB00F2EC9A /* FirebasePodTest.xcodeproj */;
				},
				{
					ProductGroup = 11D7BF412E2617EC00F2EC9A /* Products */;
					ProjectRef = 11D7BE052E2617EC00F2EC9A /* Firestore.xcodeproj */;
				},
				{
					ProductGroup = 11D7ADA12E2617EB00F2EC9A /* Products */;
					ProjectRef = 11D7AD412E2617EB00F2EC9A /* FirestoreSample.xcodeproj */;
				},
				{
					ProductGroup = 11D7BF4D2E2617EC00F2EC9A /* Products */;
					ProjectRef = 11D7B86D2E2617EC00F2EC9A /* FIRPerfE2E.xcodeproj */;
				},
				{
					ProductGroup = 11D7BF5F2E2617EC00F2EC9A /* Products */;
					ProjectRef = 11D7BE9A2E2617EC00F2EC9A /* FrameworkMaker.xcodeproj */;
				},
				{
					ProductGroup = 11D7D28B2E2617F200F2EC9A /* Products */;
					ProjectRef = 11D7D1BF2E2617F200F2EC9A /* GDTCCTWatchOSTestApp.xcodeproj */;
				},
				{
					ProductGroup = 11D7D28D2E2617F200F2EC9A /* Products */;
					ProjectRef = 11D7D25F2E2617F200F2EC9A /* GDTWatchOSTestApp.xcodeproj */;
				},
				{
					ProductGroup = 11D7BF612E2617EC00F2EC9A /* Products */;
					ProjectRef = 11D7B6412E2617EC00F2EC9A /* InAppMessaging-Example-iOS.xcodeproj */;
				},
				{
					ProductGroup = 11D7BF572E2617EC00F2EC9A /* Products */;
					ProjectRef = 11D7B6272E2617EC00F2EC9A /* InAppMessagingDisplay-Sample.xcodeproj */;
				},
				{
					ProductGroup = 11D7BF4B2E2617EC00F2EC9A /* Products */;
					ProjectRef = 11D7B79F2E2617EC00F2EC9A /* MLDownloaderTestApp.xcodeproj */;
				},
				{
					ProductGroup = 11D7BF552E2617EC00F2EC9A /* Products */;
					ProjectRef = 11D7B8B62E2617EC00F2EC9A /* PerfTestRigApp.xcodeproj */;
				},
				{
					ProductGroup = 11D7A4CB2E2617E900F2EC9A /* Products */;
					ProjectRef = 11D7A4C42E2617E900F2EC9A /* Promises.xcodeproj */;
				},
				{
					ProductGroup = 11D7BF652E2617EC00F2EC9A /* Products */;
					ProjectRef = 11D7B9622E2617EC00F2EC9A /* RemoteConfigSampleApp.xcodeproj */;
				},
				{
					ProductGroup = 11D7BF432E2617EC00F2EC9A /* Products */;
					ProjectRef = 11D7B6EE2E2617EC00F2EC9A /* Sample.xcodeproj */;
				},
				{
					ProductGroup = 11D7BF632E2617EC00F2EC9A /* Products */;
					ProjectRef = 11D7B6F92E2617EC00F2EC9A /* SampleStandaloneWatchApp.xcodeproj */;
				},
				{
					ProductGroup = 11D7AD9D2E2617EB00F2EC9A /* Products */;
					ProjectRef = 11D7AD652E2617EB00F2EC9A /* SampleWatchApp.xcodeproj */;
				},
				{
					ProductGroup = 11D7BF532E2617EC00F2EC9A /* Products */;
					ProjectRef = 11D7B9A02E2617EC00F2EC9A /* SwiftUISample.xcodeproj */;
				},
				{
					ProductGroup = 11D7BF5D2E2617EC00F2EC9A /* Products */;
					ProjectRef = 11D7B70F2E2617EC00F2EC9A /* SwiftUISample.xcodeproj */;
				},
				{
					ProductGroup = 11D7CCC92E2617EE00F2EC9A /* Products */;
					ProjectRef = 11D7CCC72E2617EE00F2EC9A /* SymbolCollisionTest.xcodeproj */;
				},
				{
					ProductGroup = 11D7D2F12E2617F300F2EC9A /* Products */;
					ProjectRef = 11D7D2C92E2617F300F2EC9A /* TestApps.xcodeproj */;
				},
				{
					ProductGroup = 11D7AD9F2E2617EB00F2EC9A /* Products */;
					ProjectRef = 11D7AD512E2617EB00F2EC9A /* tvOSSample.xcodeproj */;
				},
			);
			projectRoot = "";
			targets = (
				11BBFF072E260F30006120CA /* e-studio-dots */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		11BBFF062E260F30006120CA /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			files = (
			);
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		11BBFF042E260F30006120CA /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			files = (
			);
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		11BBFF132E260F31006120CA /* Debug configuration for PBXProject "e-studio-dots" */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = PS88LTBFRQ;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		11BBFF142E260F31006120CA /* Release configuration for PBXProject "e-studio-dots" */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = PS88LTBFRQ;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
			};
			name = Release;
		};
		11BBFF162E260F31006120CA /* Debug configuration for PBXNativeTarget "e-studio-dots" */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PS88LTBFRQ;
				ENABLE_APP_SANDBOX = YES;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SELECTED_FILES = readonly;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleURLTypes = (
					{
						CFBundleURLName = "GoogleSignIn";
						CFBundleURLSchemes = (
							"com.googleusercontent.apps.969908738521-jkk19g7fd66khugha22vvos249hrjbgo",
						);
					},
				);
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/e-studio-dots/.build/index-build/checkouts/firebase-ios-sdk/Crashlytics/UnitTests/Data",
					"$(PROJECT_DIR)/e-studio-dots/.build/index-build/checkouts/firebase-ios-sdk/Crashlytics/UnitTests/FIRCLSMachO/machO_data",
				);
				MACOSX_DEPLOYMENT_TARGET = 26.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.arno.e-studio-dots";
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_DEFAULT_ACTOR_ISOLATION = MainActor;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				XROS_DEPLOYMENT_TARGET = 26.0;
			};
			name = Debug;
		};
		11BBFF172E260F31006120CA /* Release configuration for PBXNativeTarget "e-studio-dots" */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = PS88LTBFRQ;
				ENABLE_APP_SANDBOX = YES;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				ENABLE_USER_SELECTED_FILES = readonly;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_CFBundleURLTypes = (
					{
						CFBundleURLName = "GoogleSignIn";
						CFBundleURLSchemes = (
							"com.googleusercontent.apps.969908738521-jkk19g7fd66khugha22vvos249hrjbgo",
						);
					},
				);
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 26.0;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"$(PROJECT_DIR)/e-studio-dots/.build/index-build/checkouts/firebase-ios-sdk/Crashlytics/UnitTests/Data",
					"$(PROJECT_DIR)/e-studio-dots/.build/index-build/checkouts/firebase-ios-sdk/Crashlytics/UnitTests/FIRCLSMachO/machO_data",
				);
				MACOSX_DEPLOYMENT_TARGET = 26.0;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.arno.e-studio-dots";
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = YES;
				SDKROOT = auto;
				STRING_CATALOG_GENERATE_SYMBOLS = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_DEFAULT_ACTOR_ISOLATION = MainActor;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_UPCOMING_FEATURE_MEMBER_IMPORT_VISIBILITY = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				XROS_DEPLOYMENT_TARGET = 26.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		11BBFF032E260F30006120CA /* Build configuration list for PBXProject "e-studio-dots" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				11BBFF132E260F31006120CA /* Debug configuration for PBXProject "e-studio-dots" */,
				11BBFF142E260F31006120CA /* Release configuration for PBXProject "e-studio-dots" */,
			);
			defaultConfigurationName = Release;
		};
		11BBFF152E260F31006120CA /* Build configuration list for PBXNativeTarget "e-studio-dots" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				11BBFF162E260F31006120CA /* Debug configuration for PBXNativeTarget "e-studio-dots" */,
				11BBFF172E260F31006120CA /* Release configuration for PBXNativeTarget "e-studio-dots" */,
			);
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		11F61CB72E26245B0049DF94 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/firebase/firebase-ios-sdk";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 11.15.0;
			};
		};
		11F61D022E2629260049DF94 /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/google/GoogleSignIn-iOS.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 9.0.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		11F61CB82E26245B0049DF94 /* FirebaseAnalytics */ = {
			isa = XCSwiftPackageProductDependency;
			package = 11F61CB72E26245B0049DF94 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAnalytics;
		};
		11F61CBA2E26245B0049DF94 /* FirebaseAnalyticsCore */ = {
			isa = XCSwiftPackageProductDependency;
			package = 11F61CB72E26245B0049DF94 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAnalyticsCore;
		};
		11F61CBC2E26245B0049DF94 /* FirebaseAnalyticsIdentitySupport */ = {
			isa = XCSwiftPackageProductDependency;
			package = 11F61CB72E26245B0049DF94 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAnalyticsIdentitySupport;
		};
		11F61CBE2E26245B0049DF94 /* FirebaseAnalyticsOnDeviceConversion */ = {
			isa = XCSwiftPackageProductDependency;
			package = 11F61CB72E26245B0049DF94 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAnalyticsOnDeviceConversion;
		};
		11F61CF22E2624F60049DF94 /* FirebaseCore */ = {
			isa = XCSwiftPackageProductDependency;
			package = 11F61CB72E26245B0049DF94 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseCore;
		};
		11F61D032E2629260049DF94 /* GoogleSignIn */ = {
			isa = XCSwiftPackageProductDependency;
			package = 11F61D022E2629260049DF94 /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */;
			productName = GoogleSignIn;
		};
		11F61D052E2629260049DF94 /* GoogleSignInSwift */ = {
			isa = XCSwiftPackageProductDependency;
			package = 11F61D022E2629260049DF94 /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */;
			productName = GoogleSignInSwift;
		};
		11F61D072E2629B80049DF94 /* FirebaseAuth */ = {
			isa = XCSwiftPackageProductDependency;
			package = 11F61CB72E26245B0049DF94 /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAuth;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 11BBFF002E260F30006120CA /* Project object */;
}
