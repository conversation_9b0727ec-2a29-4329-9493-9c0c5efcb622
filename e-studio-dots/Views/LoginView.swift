//
//  LoginView.swift
//  e-studio-dots
//
//  Created by Arno on 2025/7/15.
//

import SwiftUI

struct LoginView: View {
    @EnvironmentObject private var authManager: AuthenticationManager
    @State private var isLoading = false
    
    var body: some View {
        VStack(spacing: 32) {
            // App Logo and Title
            VStack(spacing: 16) {
                Image(systemName: "circle.grid.2x2.fill")
                    .font(.system(size: 60))
                    .foregroundStyle(.blue.gradient)
                
                Text("EStudio Dots")
                    .font(.largeTitle)
                    .fontWeight(.bold)
                
                Text("Log your thinking dots")
                    .font(.subheadline)
                    .foregroundStyle(.secondary)
            }
            .padding(.top, 60)
            
            Spacer()
            
            // Sign In Section
            VStack(spacing: 24) {
                // Google Sign In Button
                Button(action: {
                    Task {
                        isLoading = true
                        await authManager.signInWithGoogle()
                        isLoading = false
                    }
                }) {
                    HStack {
                        Image(systemName: "globe")
                            .font(.title2)
                        
                        Text("Continue with Google")
                            .font(.headline)
                            .fontWeight(.medium)
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .frame(height: 50)
                    .background(Color.blue.gradient)
                    .cornerRadius(12)
                }
                .disabled(isLoading)
                .opacity(isLoading ? 0.6 : 1.0)
                
                // Loading indicator
                if isLoading {
                    ProgressView()
                        .scaleEffect(0.8)
                }
                
                // Error message
                if !authManager.errorMessage.isEmpty {
                    Text(authManager.errorMessage)
                        .font(.caption)
                        .foregroundColor(.red)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                }
            }
            .padding(.horizontal, 32)
            
            Spacer()
            
            // Privacy note
            Text("By continuing, you agree to our Terms of Service and Privacy Policy")
                .font(.caption)
                .foregroundStyle(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 32)
                .padding(.bottom, 32)
        }
        .background(Color(.systemBackground))
    }
}

#Preview {
    @Previewable @StateObject var authManager = AuthenticationManager()
    
    LoginView()
        .environmentObject(authManager)
}
