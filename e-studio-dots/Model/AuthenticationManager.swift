//
//  AuthenticationManager.swift
//  e-studio-dots
//
//  Created by A<PERSON> on 2025/7/15.
//

import Foundation
import Combine
import FirebaseCore
import FirebaseAuth
import GoogleSignIn
import SwiftUI

@MainActor
class AuthenticationManager: ObservableObject {
    @Published var user: FirebaseAuth.User?
    @Published var isSignedIn = false
    @Published var errorMessage = ""
    
    private var handle: AuthStateDidChangeListenerHandle?
    
    init() {
        handle = Auth.auth().addStateDidChangeListener { [weak self] _, user in
            self?.user = user
            self?.isSignedIn = user != nil
        }
    }
    
    deinit {
        if let handle = handle {
            Auth.auth().removeStateDidChangeListener(handle)
        }
    }
    
    func signInWithGoogle() async {
        guard let clientID = FirebaseApp.app()?.options.clientID else {
            errorMessage = "No client ID found"
            return
        }
        
        // Create Google Sign In configuration object
        let config = GIDConfiguration(clientID: clientID)
        GIDSignIn.sharedInstance.configuration = config
        
        // Get the presenting view controller
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first,
              let rootViewController = window.rootViewController else {
            errorMessage = "There is no root view controller"
            return
        }
        
        do {
            let result = try await GIDSignIn.sharedInstance.signIn(withPresenting: rootViewController)
            
            guard let idToken = result.user.idToken?.tokenString else {
                errorMessage = "Failed to get ID token"
                return
            }
            
            let accessToken = result.user.accessToken.tokenString
            let credential = GoogleAuthProvider.credential(withIDToken: idToken, accessToken: accessToken)
            
            try await Auth.auth().signIn(with: credential)
            errorMessage = ""
            
        } catch {
            errorMessage = error.localizedDescription
        }
    }
    
    func signOut() {
        do {
            try Auth.auth().signOut()
            GIDSignIn.sharedInstance.signOut()
            errorMessage = ""
    } catch {
            errorMessage = error.localizedDescription
        }
    }
    
    /// Get Firebase ID Token for authenticated API requests
    /// This token should be sent in the Authorization header as "Bearer <token>"
    func getIdToken(forceRefresh: Bool = false) async throws -> String {
        guard let currentUser = Auth.auth().currentUser else {
            throw AuthError.userNotSignedIn
        }
        
        // Get a fresh ID token
        let token = try await currentUser.getIDToken(forcingRefresh: forceRefresh)
        return token
    }
    
    /// Check if the current user's token is still valid
    func isTokenValid() async -> Bool {
        do {
            _ = try await getIdToken(forceRefresh: false)
            return true
        } catch {
            return false
        }
    }
    
    func getUserInfo() -> [String: Any] {
        guard let user = user else { return [:] }
        
        return [
            "uid": user.uid,
            "email": user.email ?? "No email",
            "displayName": user.displayName ?? "No display name",
            "photoURL": user.photoURL?.absoluteString ?? "No photo URL",
            "isEmailVerified": user.isEmailVerified,
            "creationDate": user.metadata.creationDate?.description ?? "Unknown",
            "lastSignInDate": user.metadata.lastSignInDate?.description ?? "Unknown"
        ]
    }
}

// MARK: - Auth Errors
enum AuthError: LocalizedError {
    case userNotSignedIn
    case tokenExpired
    case networkError
    
    var errorDescription: String? {
        switch self {
        case .userNotSignedIn:
            return "User is not signed in"
        case .tokenExpired:
            return "Authentication token has expired"
        case .networkError:
            return "Network error occurred"
        }
    }
} 
