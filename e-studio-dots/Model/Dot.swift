import Foundation

// MARK: - Dot Models
struct Dot: Codable, Identifiable {
    let id: String
    let uuid: String
    let userId: String
    let type: Int
    let bizType: Int
    let content: DotContent
    let tags: [DotTag]
    let createdAt: String
    let updatedAt: String
}

struct DotContent: Codable {
    let text: String?
    let title: String?
    let url: String?
    let meta: String?
}

struct DotTag: Codable {
    let name: String
    let slug: String?
}

// MARK: - API Response Models
struct DotResponse: Codable {
    let success: Bool
    let data: Dot?
    let error: String?
}

struct DotsListResponse: Codable {
    let success: Bool
    let data: [Dot]?
    let pagination: Pagination?
    let error: String?
}

struct Pagination: Codable {
    let page: Int
    let pageSize: Int
    let total: Int
    let totalPages: Int
}

// MARK: - Dot Type Enums
enum DotType: Int, CaseIterable {
    case idea = 0
    case quote = 1
    case post = 2
    case note = 3
    case link = 4
    
    var displayName: String {
        switch self {
        case .idea: return "Idea"
        case .quote: return "Quote"
        case .post: return "Post"
        case .note: return "Note"
        case .link: return "Link"
        }
    }
    
    var icon: String {
        switch self {
        case .idea: return "lightbulb"
        case .quote: return "quote.bubble"
        case .post: return "doc.text"
        case .note: return "note.text"
        case .link: return "link"
        }
    }
}

enum BizType: Int, CaseIterable {
    case personal = 0
    case professional = 1
    case publicDot = 2
    
    var displayName: String {
        switch self {
        case .personal: return "Personal"
        case .professional: return "Professional"
        case .publicDot: return "Public"
        }
    }
} 