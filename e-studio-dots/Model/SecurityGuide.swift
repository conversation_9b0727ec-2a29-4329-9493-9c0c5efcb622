//
//  SecurityGuide.swift
//  e-studio-dots
//
//  Created by <PERSON><PERSON> on 2025-07-15.
//
//  SECURITY GUIDE: How to get and use Firebase Auth tokens safely

import Foundation

/*
 
 ═══════════════════════════════════════════════════════════════════════════════
 FIREBASE AUTHENTICATION TOKEN SECURITY GUIDE
 ═══════════════════════════════════════════════════════════════════════════════
 
 This guide explains how to securely get and use Firebase authentication tokens
 for API requests to protect user data and prevent security vulnerabilities.
 
 ═══════════════════════════════════════════════════════════════════════════════
 
 ## 1. GETTING THE FIREBASE ID TOKEN
 
 Firebase ID tokens are JWT tokens that contain user identity information and
 are signed by Google. They're the secure way to authenticate API requests.
 
 ### Key Properties of Firebase ID Tokens:
 • Automatically expire after 1 hour
 • Contain user UID, email, and other claims
 • Are signed by Google and can be verified by your backend
 • Cannot be forged or tampered with
 • Are automatically refreshed by the Firebase SDK
 
 ### How to Get the Token:

 In your AuthenticationManager, add this method:
 
 ```swift
 /// Get Firebase ID Token for authenticated API requests
 func getIdToken(forceRefresh: Bool = false) async throws -> String {
     guard let currentUser = Auth.auth().currentUser else {
         throw AuthError.userNotSignedIn
     }
     
     // Get a fresh ID token
     let token = try await currentUser.getIDToken(forceRefresh)
     return token
 }
 ```
 
 ═══════════════════════════════════════════════════════════════════════════════
 
 ## 2. MAKING SECURE API REQUESTS
 
 Here's how to make an authenticated API request:
 
 ```swift
 func makeSecureAPIRequest() async {
     do {
         // 1. Get the Firebase ID token
         let token = try await authManager.getIdToken()
         
         // 2. Create your API request
         guard let url = URL(string: "https://your-api.com/user/profile") else { return }
         var request = URLRequest(url: url)
         
         // 3. CRITICAL: Add token to Authorization header
         request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
         request.setValue("application/json", forHTTPHeaderField: "Content-Type")
         
         // 4. Make the request
         let (data, response) = try await URLSession.shared.data(for: request)
         
         // 5. Handle the response
         if let httpResponse = response as? HTTPURLResponse {
             switch httpResponse.statusCode {
             case 200...299:
                 // Success - process data
                 print("Success: \(String(data: data, encoding: .utf8) ?? "")")
             case 401:
                 // Token expired - get new token and retry
                 print("Token expired, need to refresh")
             case 403:
                 // Forbidden - user doesn't have permission
                 print("Access forbidden")
             default:
                 print("HTTP Error: \(httpResponse.statusCode)")
             }
         }
         
     } catch {
         print("API request failed: \(error)")
     }
 }
 ```
 
 ═══════════════════════════════════════════════════════════════════════════════
 
 ## 3. SECURITY BEST PRACTICES
 
 ### ✅ DO:
 • Always use HTTPS for API requests
 • Send token in Authorization header as "Bearer <token>"
 • Verify tokens on your backend using Firebase Admin SDK
 • Handle token expiration gracefully (401 responses)
 • Use `forceRefresh: false` to use cached valid tokens
 • Set appropriate request timeouts
 • Validate all user input on the backend
 • Use CORS properly on your backend
 • Log security events for monitoring
 
 ### ❌ DON'T:
 • Never send tokens in URL parameters
 • Never store tokens in UserDefaults or local storage
 • Never send tokens over HTTP (only HTTPS)
 • Never trust the client - always verify on backend
 • Don't cache tokens yourself (Firebase handles this)
 • Don't expose sensitive data in error messages
 • Don't make API calls when user is not authenticated
 
 ═══════════════════════════════════════════════════════════════════════════════
 
 ## 4. BACKEND TOKEN VERIFICATION
 
 Your backend MUST verify Firebase ID tokens to ensure security.
 
 ### Node.js/Express Example:
 
 ```javascript
 const admin = require('firebase-admin');
 
 // Initialize Firebase Admin SDK
 admin.initializeApp({
   credential: admin.credential.applicationDefault(),
 });
 
 // Middleware to verify Firebase ID tokens
 async function verifyFirebaseToken(req, res, next) {
   try {
     // Extract token from Authorization header
     const authHeader = req.headers.authorization;
     if (!authHeader || !authHeader.startsWith('Bearer ')) {
       return res.status(401).json({ error: 'Unauthorized: No token provided' });
     }
     
     const idToken = authHeader.substring(7); // Remove 'Bearer ' prefix
     
     // Verify the token with Firebase Admin SDK
     const decodedToken = await admin.auth().verifyIdToken(idToken);
     
     // Token is valid - add user info to request
     req.user = {
       uid: decodedToken.uid,
       email: decodedToken.email,
       emailVerified: decodedToken.email_verified,
     };
     
     next(); // Continue to the actual route handler
     
   } catch (error) {
     console.error('Token verification failed:', error);
     return res.status(401).json({ error: 'Unauthorized: Invalid token' });
   }
 }
 
 // Use the middleware on protected routes
 app.get('/api/user/profile', verifyFirebaseToken, (req, res) => {
   // req.user contains verified user information
   const userUid = req.user.uid;
   // ... fetch and return user data
 });
 ```

 ═══════════════════════════════════════════════════════════════════════════════
 
 ## 5. ERROR HANDLING AND RETRY LOGIC
 
 Handle token expiration and network errors gracefully:
 
 ```swift
 func makeAuthenticatedRequestWithRetry<T: Codable>(
     url: URL,
     method: String = "GET",
     body: Data? = nil,
     responseType: T.Type
 ) async throws -> T {
     
     var retryCount = 0
     let maxRetries = 3
     
     while retryCount < maxRetries {
         do {
             // Get fresh token (force refresh after first failure)
             let token = try await authManager.getIdToken(forceRefresh: retryCount > 0)
             
             // Create request
             var request = URLRequest(url: url)
             request.httpMethod = method
             request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
             request.setValue("application/json", forHTTPHeaderField: "Content-Type")
             
             if let body = body {
                 request.httpBody = body
             }
             
             // Make request
             let (data, response) = try await URLSession.shared.data(for: request)
             
             guard let httpResponse = response as? HTTPURLResponse else {
                 throw URLError(.badServerResponse)
             }
             
             // Handle response
             switch httpResponse.statusCode {
             case 200...299:
                 let decoder = JSONDecoder()
                 decoder.dateDecodingStrategy = .iso8601
                 return try decoder.decode(responseType, from: data)
                 
             case 401:
                 // Token expired, retry with fresh token
                 if retryCount < maxRetries - 1 {
                     retryCount += 1
                     continue
                 }
                 throw URLError(.userAuthenticationRequired)
                 
             case 403:
                 throw URLError(.noPermissionsToReadFile)
                 
             default:
                 throw URLError(.badServerResponse)
             }
             
         } catch {
             if retryCount < maxRetries - 1 {
                 retryCount += 1
                 // Exponential backoff
                 try await Task.sleep(nanoseconds: UInt64(pow(2.0, Double(retryCount)) * 1_000_000_000))
                 continue
             } else {
                 throw error
             }
         }
     }
     
     throw URLError(.timedOut)
 }
 ```
 
 ═══════════════════════════════════════════════════════════════════════════════
 
 ## 6. PRACTICAL USAGE EXAMPLES
 
 ### Fetch User Profile:
 ```swift
 func fetchUserProfile() async {
     do {
         let url = URL(string: "https://your-api.com/api/user/profile")!
         let profile: UserProfile = try await makeAuthenticatedRequestWithRetry(
             url: url,
             responseType: UserProfile.self
         )
         // Update UI with profile data
     } catch {
         // Handle error (show alert, etc.)
     }
 }
 ```
 
 ### Create a Post:
 ```swift
 func createPost(content: String) async {
     do {
         let postData = ["content": content, "timestamp": ISO8601DateFormatter().string(from: Date())]
         let body = try JSONSerialization.data(withJSONObject: postData)
         
         let url = URL(string: "https://your-api.com/api/posts")!
         let newPost: Post = try await makeAuthenticatedRequestWithRetry(
             url: url,
             method: "POST",
             body: body,
             responseType: Post.self
         )
         // Handle success
     } catch {
         // Handle error
     }
 }
 ```
 
 ═══════════════════════════════════════════════════════════════════════════════
 
 ## 7. MONITORING AND SECURITY
 
 ### Client-side monitoring:
 • Log authentication failures (without tokens)
 • Monitor API response times
 • Track unusual error patterns
 • Implement proper error reporting
 
 ### Server-side security:
 • Log all authentication attempts
 • Monitor for suspicious activity
 • Implement rate limiting
 • Set up alerts for security events
 
 ### Never log:
 • Actual authentication tokens
 • User passwords or sensitive data
 • Full request/response bodies with personal info
 
 ═══════════════════════════════════════════════════════════════════════════════
 
 ## 8. SUMMARY
 
 To securely get and use user authentication tokens:
 
 1. ✅ Use `Auth.auth().currentUser.getIDToken()` to get Firebase ID tokens
 2. ✅ Send tokens in Authorization header: "Bearer <token>"
 3. ✅ Always use HTTPS
 4. ✅ Verify tokens on your backend with Firebase Admin SDK
 5. ✅ Handle 401 responses by refreshing tokens
 6. ✅ Implement retry logic with exponential backoff
 7. ✅ Never store or cache tokens manually
 8. ✅ Monitor for security events
 
 This approach ensures:
 • User data remains secure
 • Authentication cannot be forged
 • Automatic token management
 • Proper error handling
 • Scalable and maintainable code
 
 The Firebase ID token is your secure key - treat it with care and never expose it.
 
 ═══════════════════════════════════════════════════════════════════════════════
 
 */

// MARK: - Example Data Models

struct UserProfile: Codable {
    let uid: String
    let email: String?
    let displayName: String?
    let photoURL: String?
    let createdAt: Date
    let lastActiveAt: Date
}

struct Post: Codable {
    let id: String
    let content: String
    let authorUID: String
    let createdAt: Date
    let likesCount: Int
} 