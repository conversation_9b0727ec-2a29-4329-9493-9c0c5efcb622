import SwiftUI
import FirebaseAuth

struct MainDemoView: View {
    @EnvironmentObject private var authManager: AuthenticationManager
    @State private var dotId = "128"
    @State private var dotInfo = "No dot fetched yet"
    @State private var isLoading = false
    @State private var errorMessage = ""
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Authentication Status
                VStack(spacing: 16) {
                    Text("Authentication Status")
                        .font(.headline)
                    
                    if authManager.isSignedIn {
                        VStack(spacing: 8) {
                            Text("✅ Signed In")
                                .foregroundColor(.green)
                                .font(.title2)
                            
                            if let user = authManager.user {
                                Text("Email: \(user.email ?? "No email")")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                
                                Text("UID: \(user.uid)")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            
                            Button("Sign Out") {
                                authManager.signOut()
                            }
                            .buttonStyle(.borderedProminent)
                        }
                    } else {
                        VStack(spacing: 8) {
                            Text("❌ Not Signed In")
                                .foregroundColor(.red)
                                .font(.title2)
                            
                            But<PERSON>("Sign In with Google") {
                                Task {
                                    await authManager.signInWithGoogle()
                                }
                            }
                            .buttonStyle(.borderedProminent)
                        }
                    }
                    
                    if !authManager.errorMessage.isEmpty {
                        Text("Error: \(authManager.errorMessage)")
                            .font(.caption)
                            .foregroundColor(.red)
                            .multilineTextAlignment(.center)
                    }
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(12)
                
                Divider()
                
                // Dot Fetching Demo
                VStack(spacing: 16) {
                    Text("Dot Fetching Demo")
                        .font(.headline)
                    
                    HStack {
                        Text("Dot ID:")
                        TextField("Enter dot ID", text: $dotId)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                            .keyboardType(.numberPad)
                    }
                    
                    Button("Fetch Dot") {
                        fetchDot()
                    }
                    .buttonStyle(.borderedProminent)
                    .disabled(!authManager.isSignedIn || isLoading)
                    
                    if isLoading {
                        ProgressView("Fetching dot...")
                    }
                    
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Result:")
                            .font(.subheadline)
                            .fontWeight(.medium)
                        
                        ScrollView {
                            Text(dotInfo)
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .frame(maxWidth: .infinity, alignment: .leading)
                        }
                        .frame(maxHeight: 200)
                    }
                    .padding()
                    .background(Color.gray.opacity(0.1))
                    .cornerRadius(8)
                    
                    if !errorMessage.isEmpty {
                        Text("Error: \(errorMessage)")
                            .font(.caption)
                            .foregroundColor(.red)
                            .multilineTextAlignment(.center)
                    }
                }
                .padding()
                .background(Color.gray.opacity(0.1))
                .cornerRadius(12)
                
                Spacer()
            }
            .padding()
            .navigationTitle("EStudio Dots Demo")
        }
    }
    
    private func fetchDot() {
        guard authManager.isSignedIn else {
            errorMessage = "Please sign in first"
            return
        }
        
        isLoading = true
        errorMessage = ""
        
        Task {
            do {
                let token = try await authManager.getIdToken()
                
                // Make HTTP request to fetch dot
                let url = URL(string: "http://localhost:3002/api/dots/\(dotId)")!
                var request = URLRequest(url: url)
                request.httpMethod = "GET"
                request.setValue("application/json", forHTTPHeaderField: "Content-Type")
                request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
                request.setValue("true", forHTTPHeaderField: "x-firebase-token-auth")
                
                let (data, response) = try await URLSession.shared.data(for: request)
                
                guard let httpResponse = response as? HTTPURLResponse else {
                    throw URLError(.badServerResponse)
                }
                
                await MainActor.run {
                    if httpResponse.statusCode == 200 {
                        if let jsonString = String(data: data, encoding: .utf8) {
                            dotInfo = "Success! Response:\n\(jsonString)"
                        } else {
                            dotInfo = "Success! Got data but couldn't convert to string"
                        }
                    } else {
                        let errorData = String(data: data, encoding: .utf8) ?? "No error data"
                        dotInfo = "HTTP \(httpResponse.statusCode): \(errorData)"
                    }
                    isLoading = false
                }
                
            } catch {
                await MainActor.run {
                    errorMessage = error.localizedDescription
                    dotInfo = "Failed to fetch dot"
                    isLoading = false
                }
            }
        }
    }
}

#Preview {
    @Previewable @StateObject var authManager = AuthenticationManager()
    
    MainDemoView()
        .environmentObject(authManager)
} 
